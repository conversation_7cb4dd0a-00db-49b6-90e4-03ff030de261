<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin Noujoum Store',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '+222 45 67 89 00',
            'company_name' => 'Noujoum Store',
            'website' => 'https://noujoumstore.mr',
            'bio' => 'Administrateur de la plateforme Noujoum Store Marketplace',
            'role' => 'admin',
            'is_verified' => true,
            'email_verified_at' => now(),
            'is_admin' => true,
        ]);

        // Create sample developers
        $developers = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+222 45 67 89 01',
                'company_name' => 'TechSolutions SARL',
                'website' => 'https://techsolutions.mr',
                'bio' => 'Développeur d\'applications d\'entreprise spécialisé dans les solutions CRM',
                'role' => 'user',
                'is_verified' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Fatima Mint Abdallahi',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+222 45 67 89 06',
                'company_name' => 'Développeur Indépendant',
                'website' => 'https://desertweather.mr',
                'bio' => 'Développeuse indépendante spécialisée dans les applications météorologiques',
                'role' => 'user',
                'is_verified' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Mohamed Ould Salem',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+222 45 67 89 04',
                'company_name' => 'EduTech SARL',
                'website' => 'https://edutech.mr',
                'bio' => 'Fondateur d\'EduTech, spécialisé dans les plateformes éducatives',
                'role' => 'user',
                'is_verified' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Aisha Mint Mohamed',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+222 45 67 89 08',
                'company_name' => 'FinTech Mauritanie SARL',
                'website' => 'https://fintech.mr',
                'bio' => 'Experte en solutions financières numériques pour la Mauritanie',
                'role' => 'user',
                'is_verified' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Omar Ould Ahmed',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+222 45 67 89 07',
                'company_name' => 'Urban Mobility SARL',
                'website' => 'https://urbanmobility.mr',
                'bio' => 'Entrepreneur dans le domaine de la mobilité urbaine',
                'role' => 'user',
                'is_verified' => false,
                'email_verified_at' => now(),
            ],
        ];

        foreach ($developers as $developer) {
            User::create($developer);
        }
    }
}
