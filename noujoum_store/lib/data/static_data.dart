import '../models/mauritanian_app.dart';

/// Static data containing all Mauritanian applications for the Noujoum Store Marketplace
/// @deprecated This class is deprecated. Use AppRepository and API services instead.
@Deprecated('Use AppRepository and API services instead of static data')
class StaticData {

  static final List<MauritanianApp> apps = [
    MauritanianApp(
      id: 'mauricrm_pro',
      appName: 'MauriCRM Pro',
      tagline: 'Système de gestion client professionnel',
      description: 'Solution CRM complète pour les entreprises mauritaniennes',
      detailedDescription: 'MauriCRM Pro est une solution de gestion de la relation client spécialement conçue pour les entreprises mauritaniennes. Gérez vos contacts, suivez vos ventes, automatisez votre marketing et améliorez votre service client.',
      developerName: 'TechSolutions Mauritanie',
      developerEmail: '<EMAIL>',
      developerPhone: '+222 45 67 89 01',
      companyName: 'TechSolutions SARL',
      developerWebsite: 'https://techsolutions.mr',
      appType: AppType.saas,
      supportedPlatforms: [Platform.web, Platform.android, Platform.iOS],
      iconUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=150&h=150&fit=crop&crop=center',
      screenshots: [
        'https://images.unsplash.com/photo-**********-bebda4e38f71?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=300&h=600&fit=crop',
      ],
      licenseType: LicenseType.monthly,
      pricingModel: PricingModel.paid,
      pricing: '500 MRU/mois',
      hasFreeTrial: true,
      trialDays: 14,
      targetAudience: 'PME et grandes entreprises',
      businessSectors: ['Commerce', 'Services', 'Industrie'],
      businessValue: 'Améliore la gestion client et augmente les ventes de 30%',
      keyFeatures: ['Gestion contacts', 'Suivi ventes', 'Rapports avancés', 'Intégration email'],
      supportOptions: [SupportType.email, SupportType.phone, SupportType.training],
      languages: ['Français', 'Arabe', 'Hassaniya'],
      downloads: 2500,
      activeUsers: 1200,
      publishDate: DateTime(2024, 1, 15),
      lastUpdate: DateTime(2024, 8, 1),
      isVerified: true,
      isFeatured: true,
      primaryCategory: 'business_management',
      subcategory: 'Systèmes CRM',
      tags: ['crm', 'ventes', 'clients', 'business'],
      rating: 4.6,
    ),

    MauritanianApp(
      id: 'pharmacie_finder',
      appName: 'Pharmacie Finder MR',
      tagline: 'Trouvez les pharmacies ouvertes',
      description: 'Trouvez rapidement les pharmacies ouvertes près de chez vous à Nouakchott et dans toute la Mauritanie. Vérifiez la disponibilité des médicaments.',
      detailedDescription: 'Pharmacie Finder MR vous aide à localiser rapidement les pharmacies ouvertes dans votre région. Consultez les horaires, vérifiez la disponibilité des médicaments et obtenez des directions précises.',
      developerName: 'TechMed Mauritanie',
      developerEmail: '<EMAIL>',
      developerPhone: '+222 45 67 89 02',
      companyName: 'TechMed SARL',
      developerWebsite: 'https://techmed.mr',
      appType: AppType.mobile,
      supportedPlatforms: [Platform.android, Platform.iOS],
      iconUrl: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=150&h=150&fit=crop&crop=center',
      screenshots: [
        'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1584820927498-cfe5211fd8bf?w=300&h=600&fit=crop',
      ],
      licenseType: LicenseType.free,
      pricingModel: PricingModel.free,
      pricing: 'Gratuit',
      hasFreeTrial: false,
      trialDays: 0,
      targetAudience: 'Grand public',
      businessSectors: ['Santé', 'Services publics'],
      businessValue: 'Facilite l\'accès aux soins pharmaceutiques',
      keyFeatures: ['Localisation pharmacies', 'Horaires d\'ouverture', 'Disponibilité médicaments', 'Navigation GPS'],
      supportOptions: [SupportType.email],
      languages: ['Français', 'Arabe'],
      downloads: 8500,
      activeUsers: 6200,
      publishDate: DateTime(2024, 2, 10),
      lastUpdate: DateTime(2024, 7, 15),
      isVerified: true,
      isFeatured: false,
      primaryCategory: 'health',
      subcategory: 'Pharmacie',
      tags: ['pharmacie', 'médicaments', 'urgence', 'localisation'],
      rating: 4.2,
    ),

    // Education Applications
    MauritanianApp(
      id: 'hassaniya_learn',
      appName: 'Hassaniya Learn',
      tagline: 'Maîtrisez le hassaniya mauritanien',
      description: 'Apprenez le dialecte hassaniya mauritanien avec des leçons interactives, des exercices de prononciation et des conversations pratiques.',
      detailedDescription: 'Hassaniya Learn propose une méthode d\'apprentissage complète du dialecte hassaniya avec des leçons progressives, des exercices de prononciation et des conversations authentiques.',
      developerName: 'Linguistic Solutions MR',
      developerEmail: '<EMAIL>',
      developerPhone: '+222 45 67 89 03',
      companyName: 'Linguistic Solutions SARL',
      developerWebsite: 'https://linguisticsolutions.mr',
      appType: AppType.mobile,
      supportedPlatforms: [Platform.android, Platform.iOS],
      iconUrl: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=150&h=150&fit=crop&crop=center',
      screenshots: [
        'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=300&h=600&fit=crop',
      ],
      licenseType: LicenseType.monthly,
      pricingModel: PricingModel.freemium,
      pricing: 'Gratuit avec options premium',
      hasFreeTrial: true,
      trialDays: 7,
      targetAudience: 'Étudiants et expatriés',
      businessSectors: ['Éducation', 'Culture'],
      businessValue: 'Préserve et transmet la culture hassaniya',
      keyFeatures: ['Leçons interactives', 'Exercices prononciation', 'Conversations pratiques', 'Suivi progrès'],
      supportOptions: [SupportType.email, SupportType.chat],
      languages: ['Français', 'Arabe', 'Hassaniya'],
      downloads: 12000,
      activeUsers: 8500,
      publishDate: DateTime(2024, 1, 20),
      lastUpdate: DateTime(2024, 8, 5),
      isVerified: true,
      isFeatured: true,
      primaryCategory: 'education',
      subcategory: 'Langues',
      tags: ['hassaniya', 'langue', 'apprentissage', 'culture'],
      rating: 4.7,
    ),

    MauritanianApp(
      id: 'ecole_numerique',
      appName: 'École Numérique MR',
      tagline: 'L\'école à portée de main',
      description: 'Plateforme d\'apprentissage en ligne pour les élèves mauritaniens. Cours de mathématiques, sciences, français et arabe adaptés au programme national.',
      detailedDescription: 'École Numérique MR offre une plateforme complète d\'apprentissage en ligne avec des cours interactifs, des exercices pratiques et un suivi personnalisé pour tous les niveaux scolaires.',
      developerName: 'EduTech Mauritanie',
      developerEmail: '<EMAIL>',
      developerPhone: '+222 45 67 89 04',
      companyName: 'EduTech SARL',
      developerWebsite: 'https://edutech.mr',
      appType: AppType.web,
      supportedPlatforms: [Platform.web, Platform.android, Platform.iOS],
      iconUrl: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=150&h=150&fit=crop&crop=center',
      screenshots: [
        'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=300&h=600&fit=crop',
      ],
      licenseType: LicenseType.monthly,
      pricingModel: PricingModel.freemium,
      pricing: 'Gratuit avec abonnement premium',
      hasFreeTrial: true,
      trialDays: 30,
      targetAudience: 'Élèves et enseignants',
      businessSectors: ['Éducation', 'Formation'],
      businessValue: 'Améliore les résultats scolaires de 40%',
      keyFeatures: ['Cours interactifs', 'Exercices adaptatifs', 'Suivi progrès', 'Programme national'],
      supportOptions: [SupportType.email, SupportType.phone, SupportType.chat],
      languages: ['Français', 'Arabe'],
      downloads: 20000,
      activeUsers: 15000,
      publishDate: DateTime(2024, 3, 5),
      lastUpdate: DateTime(2024, 8, 10),
      isVerified: true,
      isFeatured: true,
      primaryCategory: 'education',
      subcategory: 'E-learning',
      tags: ['éducation', 'cours', 'élèves', 'programme'],
      rating: 4.4,
    ),

    // Agriculture Applications
    MauritanianApp(
      id: 'nomad_tracker',
      appName: 'Nomad Tracker',
      tagline: 'Gestion moderne du bétail nomade',
      description: 'Application de gestion du bétail pour les éleveurs nomades. Suivez vos troupeaux, gérez la santé animale et trouvez les meilleurs pâturages.',
      detailedDescription: 'Nomad Tracker révolutionne la gestion pastorale traditionnelle avec des outils numériques adaptés aux éleveurs nomades mauritaniens.',
      developerName: 'Pastoral Tech',
      developerEmail: '<EMAIL>',
      developerPhone: '+222 45 67 89 05',
      companyName: 'Pastoral Tech SARL',
      developerWebsite: 'https://pastoraltech.mr',
      appType: AppType.mobile,
      supportedPlatforms: [Platform.android, Platform.iOS],
      iconUrl: 'https://images.unsplash.com/photo-1500595046743-cd271d694d30?w=150&h=150&fit=crop&crop=center',
      screenshots: [
        'https://images.unsplash.com/photo-**********-40f1f1eb26a0?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1574943320219-553eb213f72d?w=300&h=600&fit=crop',
      ],
      licenseType: LicenseType.yearly,
      pricingModel: PricingModel.paid,
      pricing: '2000 MRU/an',
      hasFreeTrial: true,
      trialDays: 30,
      targetAudience: 'Éleveurs nomades',
      businessSectors: ['Agriculture', 'Élevage'],
      businessValue: 'Optimise la productivité pastorale de 25%',
      keyFeatures: ['Suivi troupeaux', 'Gestion santé', 'Localisation pâturages', 'Météo locale'],
      supportOptions: [SupportType.phone, SupportType.training],
      languages: ['Français', 'Arabe', 'Hassaniya'],
      downloads: 5500,
      activeUsers: 3200,
      publishDate: DateTime(2024, 2, 15),
      lastUpdate: DateTime(2024, 7, 20),
      isVerified: true,
      isFeatured: false,
      primaryCategory: 'agriculture',
      subcategory: 'Élevage',
      tags: ['élevage', 'bétail', 'nomade', 'pâturage'],
      rating: 4.3,
    ),

    MauritanianApp(
      id: 'desert_weather',
      appName: 'Desert Weather MR',
      tagline: 'Météo précise du désert mauritanien',
      description: 'Prévisions météorologiques précises pour la Mauritanie. Alertes tempêtes de sable, prévisions agricoles et conditions de navigation.',
      detailedDescription: 'Desert Weather MR fournit des prévisions météorologiques spécialisées pour les conditions désertiques mauritaniennes avec des alertes en temps réel.',
      developerName: 'Fatima Mint Abdallahi',
      developerEmail: '<EMAIL>',
      developerPhone: '+222 45 67 89 06',
      companyName: 'Développeur Indépendant',
      developerWebsite: 'https://desertweather.mr',
      appType: AppType.mobile,
      supportedPlatforms: [Platform.android, Platform.iOS],
      iconUrl: 'https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=150&h=150&fit=crop&crop=center',
      screenshots: [
        'https://images.unsplash.com/photo-1592210454359-9043f067919b?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1446329813274-7c9036bd9a1f?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=600&fit=crop',
      ],
      licenseType: LicenseType.free,
      pricingModel: PricingModel.free,
      pricing: 'Gratuit',
      hasFreeTrial: false,
      trialDays: 0,
      targetAudience: 'Agriculteurs et navigateurs',
      businessSectors: ['Agriculture', 'Transport maritime'],
      businessValue: 'Prévient les pertes agricoles liées au climat',
      keyFeatures: ['Prévisions précises', 'Alertes tempêtes', 'Données agricoles', 'Navigation maritime'],
      supportOptions: [SupportType.email],
      languages: ['Français', 'Arabe'],
      downloads: 18000,
      activeUsers: 12000,
      publishDate: DateTime(2024, 1, 30),
      lastUpdate: DateTime(2024, 8, 1),
      isVerified: true,
      isFeatured: true,
      primaryCategory: 'agriculture',
      subcategory: 'Météo',
      tags: ['météo', 'tempête', 'agriculture', 'prévision'],
      rating: 4.6,
    ),

    MauritanianApp(
      id: 'nouakchott_taxi',
      appName: 'Nouakchott Taxi',
      tagline: 'Votre taxi en un clic',
      description: 'Service de taxi à la demande pour Nouakchott. Réservez votre course, suivez votre chauffeur en temps réel et payez facilement.',
      detailedDescription: 'Nouakchott Taxi révolutionne le transport urbain avec une plateforme moderne de réservation de taxis. Géolocalisation, paiement sécurisé et suivi en temps réel.',
      developerName: 'Urban Mobility MR',
      developerEmail: '<EMAIL>',
      developerPhone: '+222 45 67 89 07',
      companyName: 'Urban Mobility SARL',
      developerWebsite: 'https://urbanmobility.mr',
      appType: AppType.mobile,
      supportedPlatforms: [Platform.android, Platform.iOS],
      iconUrl: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=150&h=150&fit=crop&crop=center',
      screenshots: [
        'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=300&h=600&fit=crop',
      ],
      licenseType: LicenseType.free,
      pricingModel: PricingModel.enterprise,
      pricing: 'Commission sur les courses',
      hasFreeTrial: false,
      trialDays: 0,
      targetAudience: 'Résidents de Nouakchott',
      businessSectors: ['Transport', 'Services urbains'],
      businessValue: 'Améliore la mobilité urbaine de 50%',
      keyFeatures: ['Réservation instantanée', 'Suivi temps réel', 'Paiement mobile', 'Évaluation chauffeurs'],
      supportOptions: [SupportType.phone, SupportType.chat],
      languages: ['Français', 'Arabe', 'Hassaniya'],
      downloads: 25000,
      activeUsers: 18000,
      publishDate: DateTime(2024, 3, 10),
      lastUpdate: DateTime(2024, 8, 15),
      isVerified: true,
      isFeatured: true,
      primaryCategory: 'transport',
      subcategory: 'Taxi',
      tags: ['taxi', 'transport', 'nouakchott', 'réservation'],
      rating: 4.1,
    ),

    MauritanianApp(
      id: 'chinguetti_banking',
      appName: 'Chinguetti Banking',
      tagline: 'Votre banque dans votre poche',
      description: 'Solution bancaire mobile complète. Gérez vos comptes, effectuez des virements, payez vos factures et suivez vos dépenses.',
      detailedDescription: 'Chinguetti Banking offre une expérience bancaire mobile complète avec sécurité renforcée, interface intuitive et services financiers avancés pour les Mauritaniens.',
      developerName: 'FinTech Mauritanie',
      developerEmail: '<EMAIL>',
      developerPhone: '+222 45 67 89 08',
      companyName: 'FinTech Mauritanie SARL',
      developerWebsite: 'https://fintech.mr',
      appType: AppType.mobile,
      supportedPlatforms: [Platform.android, Platform.iOS],
      iconUrl: 'https://images.unsplash.com/photo-**********-824ae1b704d3?w=150&h=150&fit=crop&crop=center',
      screenshots: [
        'https://images.unsplash.com/photo-**********-bebda4e38f71?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-*************-ebec7560ff3e?w=300&h=600&fit=crop',
      ],
      licenseType: LicenseType.free,
      pricingModel: PricingModel.free,
      pricing: 'Gratuit pour les clients',
      hasFreeTrial: false,
      trialDays: 0,
      targetAudience: 'Clients bancaires',
      businessSectors: ['Finance', 'Services bancaires'],
      businessValue: 'Digitalise les services bancaires mauritaniens',
      keyFeatures: ['Gestion comptes', 'Virements instantanés', 'Paiement factures', 'Historique transactions'],
      supportOptions: [SupportType.phone, SupportType.email, SupportType.chat],
      languages: ['Français', 'Arabe'],
      downloads: 35000,
      activeUsers: 28000,
      publishDate: DateTime(2024, 2, 20),
      lastUpdate: DateTime(2024, 8, 12),
      isVerified: true,
      isFeatured: true,
      primaryCategory: 'finance',
      subcategory: 'Banque Mobile',
      tags: ['banque', 'mobile', 'virement', 'factures'],
      rating: 4.8,
    ),

    MauritanianApp(
      id: 'quran_mauritanie',
      appName: 'Coran Mauritanie',
      tagline: 'Le Saint Coran avec récitation mauritanienne',
      description: 'Application complète du Saint Coran avec récitations de qaris mauritaniens, traductions en français et hassaniya, et horaires de prière pour toutes les villes mauritaniennes.',
      detailedDescription: 'Coran Mauritanie offre une expérience spirituelle authentique avec des récitations de célèbres qaris mauritaniens, des traductions précises et des fonctionnalités adaptées aux besoins des musulmans mauritaniens.',
      developerName: 'Islamic Apps Mauritanie',
      developerEmail: '<EMAIL>',
      developerPhone: '+222 45 67 89 09',
      companyName: 'Islamic Apps SARL',
      developerWebsite: 'https://islamicapps.mr',
      appType: AppType.mobile,
      supportedPlatforms: [Platform.android, Platform.iOS],
      iconUrl: 'https://images.unsplash.com/photo-**********-0983c9c9ad53?w=150&h=150&fit=crop&crop=center',
      screenshots: [
        'https://images.unsplash.com/photo-1609599006353-e629aaabfeae?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1564769625905-50e93615e769?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1591604129939-f1efa4d9f7fa?w=300&h=600&fit=crop',
      ],
      licenseType: LicenseType.free,
      pricingModel: PricingModel.free,
      pricing: 'Gratuit',
      hasFreeTrial: false,
      trialDays: 0,
      targetAudience: 'Communauté musulmane',
      businessSectors: ['Religion', 'Éducation spirituelle'],
      businessValue: 'Facilite la pratique religieuse quotidienne',
      keyFeatures: ['Récitation mauritanienne', 'Traduction hassaniya', 'Horaires prière', 'Qibla direction'],
      supportOptions: [SupportType.email, SupportType.chat],
      languages: ['Arabe', 'Français', 'Hassaniya'],
      downloads: 45000,
      activeUsers: 32000,
      publishDate: DateTime(2024, 1, 10),
      lastUpdate: DateTime(2024, 8, 20),
      isVerified: true,
      isFeatured: true,
      primaryCategory: 'religion',
      subcategory: 'Islam',
      tags: ['coran', 'islam', 'prière', 'récitation'],
      rating: 4.9,
    ),

    MauritanianApp(
      id: 'souk_mauritanie',
      appName: 'Souk Mauritanie',
      tagline: 'Le marché en ligne mauritanien',
      description: 'Plateforme de commerce électronique 100% mauritanienne. Achetez et vendez des produits locaux, artisanat traditionnel, et biens de consommation.',
      detailedDescription: 'Souk Mauritanie connecte acheteurs et vendeurs mauritaniens sur une plateforme sécurisée avec paiement mobile, livraison locale et support en langues locales.',
      developerName: 'E-Commerce MR',
      developerEmail: '<EMAIL>',
      developerPhone: '+222 45 67 89 10',
      companyName: 'E-Commerce Mauritanie SARL',
      developerWebsite: 'https://ecommerce.mr',
      appType: AppType.mobile,
      supportedPlatforms: [Platform.android, Platform.iOS, Platform.web],
      iconUrl: 'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=150&h=150&fit=crop&crop=center',
      screenshots: [
        'https://images.unsplash.com/photo-**********-824ae1b704d3?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-**********-f7e723f7b9b0?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1472851294608-062f824d29cc?w=300&h=600&fit=crop',
      ],
      licenseType: LicenseType.free,
      pricingModel: PricingModel.enterprise,
      pricing: 'Gratuit avec commission',
      hasFreeTrial: false,
      trialDays: 0,
      targetAudience: 'Commerçants et consommateurs',
      businessSectors: ['Commerce', 'Artisanat', 'Retail'],
      businessValue: 'Digitalise le commerce mauritanien',
      keyFeatures: ['Marketplace local', 'Paiement mobile', 'Livraison tracking', 'Artisanat traditionnel'],
      supportOptions: [SupportType.phone, SupportType.email, SupportType.chat],
      languages: ['Français', 'Arabe', 'Hassaniya'],
      downloads: 28000,
      activeUsers: 19000,
      publishDate: DateTime(2024, 2, 25),
      lastUpdate: DateTime(2024, 8, 18),
      isVerified: true,
      isFeatured: true,
      primaryCategory: 'commerce',
      subcategory: 'Marketplace',
      tags: ['commerce', 'achat', 'vente', 'artisanat'],
      rating: 4.3,
    ),

    MauritanianApp(
      id: 'e_gov_mauritanie',
      appName: 'E-Gov Mauritanie',
      tagline: 'Services gouvernementaux digitaux',
      description: 'Accédez aux services gouvernementaux mauritaniens en ligne. Demandes de documents, paiement taxes, suivi dossiers administratifs.',
      detailedDescription: 'E-Gov Mauritanie simplifie les démarches administratives avec des services numériques sécurisés pour les citoyens mauritaniens.',
      developerName: 'Ministère de la Modernisation',
      developerEmail: '<EMAIL>',
      developerPhone: '+222 45 25 30 35',
      companyName: 'Gouvernement de Mauritanie',
      developerWebsite: 'https://egov.gov.mr',
      appType: AppType.web,
      supportedPlatforms: [Platform.web, Platform.android, Platform.iOS],
      iconUrl: 'https://images.unsplash.com/photo-**********-6726b3ff858f?w=150&h=150&fit=crop&crop=center',
      screenshots: [
        'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-**********-bebda4e38f71?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=300&h=600&fit=crop',
      ],
      licenseType: LicenseType.free,
      pricingModel: PricingModel.free,
      pricing: 'Gratuit',
      hasFreeTrial: false,
      trialDays: 0,
      targetAudience: 'Citoyens mauritaniens',
      businessSectors: ['Administration', 'Services publics'],
      businessValue: 'Modernise l\'administration publique',
      keyFeatures: ['Demandes documents', 'Paiement taxes', 'Suivi dossiers', 'Authentification sécurisée'],
      supportOptions: [SupportType.phone, SupportType.email],
      languages: ['Français', 'Arabe'],
      downloads: 65000,
      activeUsers: 48000,
      publishDate: DateTime(2024, 3, 15),
      lastUpdate: DateTime(2024, 8, 25),
      isVerified: true,
      isFeatured: true,
      primaryCategory: 'government',
      subcategory: 'Services Publics',
      tags: ['gouvernement', 'administration', 'documents', 'taxes'],
      rating: 4.2,
    ),

    MauritanianApp(
      id: 'discover_mauritania',
      appName: 'Discover Mauritania',
      tagline: 'Explorez les merveilles de la Mauritanie',
      description: 'Guide touristique complet de la Mauritanie. Découvrez Chinguetti, l\'Adrar, le Banc d\'Arguin et les traditions nomades.',
      detailedDescription: 'Discover Mauritania est votre compagnon de voyage pour explorer les sites historiques, les paysages désertiques et la riche culture mauritanienne.',
      developerName: 'Tourism Development MR',
      developerEmail: '<EMAIL>',
      developerPhone: '+222 45 67 89 11',
      companyName: 'Tourism Development SARL',
      developerWebsite: 'https://tourism.mr',
      appType: AppType.mobile,
      supportedPlatforms: [Platform.android, Platform.iOS],
      iconUrl: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=150&h=150&fit=crop&crop=center',
      screenshots: [
        'https://images.unsplash.com/photo-**********-392fe2489ffa?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1516026672322-bc52d61a55d5?w=300&h=600&fit=crop',
      ],
      licenseType: LicenseType.free,
      pricingModel: PricingModel.freemium,
      pricing: 'Gratuit avec guides premium',
      hasFreeTrial: true,
      trialDays: 14,
      targetAudience: 'Touristes et voyageurs',
      businessSectors: ['Tourisme', 'Culture', 'Patrimoine'],
      businessValue: 'Promeut le tourisme mauritanien',
      keyFeatures: ['Guides interactifs', 'Cartes offline', 'Histoire culturelle', 'Itinéraires personnalisés'],
      supportOptions: [SupportType.email, SupportType.chat],
      languages: ['Français', 'Anglais', 'Arabe'],
      downloads: 15000,
      activeUsers: 8500,
      publishDate: DateTime(2024, 4, 5),
      lastUpdate: DateTime(2024, 8, 10),
      isVerified: true,
      isFeatured: false,
      primaryCategory: 'tourism',
      subcategory: 'Guide Voyage',
      tags: ['tourisme', 'culture', 'voyage', 'patrimoine'],
      rating: 4.5,
    ),

    MauritanianApp(
      id: 'sante_plus_mr',
      appName: 'Santé Plus MR',
      tagline: 'Votre santé, notre priorité',
      description: 'Plateforme de télémédecine mauritanienne. Consultations en ligne, suivi médical, rappels médicaments et urgences médicales.',
      detailedDescription: 'Santé Plus MR révolutionne l\'accès aux soins de santé en Mauritanie avec des consultations à distance et un suivi médical personnalisé.',
      developerName: 'HealthTech Mauritanie',
      developerEmail: '<EMAIL>',
      developerPhone: '+222 45 67 89 12',
      companyName: 'HealthTech SARL',
      developerWebsite: 'https://healthtech.mr',
      appType: AppType.mobile,
      supportedPlatforms: [Platform.android, Platform.iOS, Platform.web],
      iconUrl: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=150&h=150&fit=crop&crop=center',
      screenshots: [
        'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1584820927498-cfe5211fd8bf?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=600&fit=crop',
      ],
      licenseType: LicenseType.monthly,
      pricingModel: PricingModel.freemium,
      pricing: 'Consultations payantes',
      hasFreeTrial: true,
      trialDays: 7,
      targetAudience: 'Patients et médecins',
      businessSectors: ['Santé', 'Télémédecine'],
      businessValue: 'Améliore l\'accès aux soins de santé',
      keyFeatures: ['Téléconsultations', 'Dossier médical', 'Rappels médicaments', 'Urgences 24/7'],
      supportOptions: [SupportType.phone, SupportType.email, SupportType.chat],
      languages: ['Français', 'Arabe'],
      downloads: 22000,
      activeUsers: 14000,
      publishDate: DateTime(2024, 3, 20),
      lastUpdate: DateTime(2024, 8, 22),
      isVerified: true,
      isFeatured: true,
      primaryCategory: 'health',
      subcategory: 'Télémédecine',
      tags: ['santé', 'médecin', 'consultation', 'urgence'],
      rating: 4.4,
    ),

    MauritanianApp(
      id: 'sahara_news',
      appName: 'Sahara News',
      tagline: 'L\'actualité mauritanienne en temps réel',
      description: 'Application d\'actualités mauritaniennes avec news locales, analyses politiques, sport, culture et météo en temps réel.',
      detailedDescription: 'Sahara News vous tient informé de l\'actualité mauritanienne et internationale avec des contenus en français, arabe et hassaniya.',
      developerName: 'Media Solutions MR',
      developerEmail: '<EMAIL>',
      developerPhone: '+222 45 67 89 13',
      companyName: 'Media Solutions SARL',
      developerWebsite: 'https://saharanews.mr',
      appType: AppType.mobile,
      supportedPlatforms: [Platform.android, Platform.iOS, Platform.web],
      iconUrl: 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=150&h=150&fit=crop&crop=center',
      screenshots: [
        'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=300&h=600&fit=crop',
      ],
      licenseType: LicenseType.free,
      pricingModel: PricingModel.freemium,
      pricing: 'Gratuit avec premium',
      hasFreeTrial: true,
      trialDays: 30,
      targetAudience: 'Grand public',
      businessSectors: ['Média', 'Information', 'Journalisme'],
      businessValue: 'Informe la population mauritanienne',
      keyFeatures: ['News temps réel', 'Analyses politiques', 'Sport local', 'Notifications push'],
      supportOptions: [SupportType.email],
      languages: ['Français', 'Arabe', 'Hassaniya'],
      downloads: 38000,
      activeUsers: 25000,
      publishDate: DateTime(2024, 2, 5),
      lastUpdate: DateTime(2024, 8, 28),
      isVerified: true,
      isFeatured: false,
      primaryCategory: 'news',
      subcategory: 'Actualités',
      tags: ['news', 'actualités', 'politique', 'sport'],
      rating: 4.1,
    ),

    MauritanianApp(
      id: 'thieboudienne_delivery',
      appName: 'Thieboudienne Delivery',
      tagline: 'La cuisine mauritanienne à domicile',
      description: 'Commandez les meilleurs plats mauritaniens : thieboudienne, couscous, méchoui et pâtisseries traditionnelles livrés chez vous.',
      detailedDescription: 'Thieboudienne Delivery connecte les restaurants mauritaniens authentiques avec les amateurs de cuisine locale pour des livraisons rapides.',
      developerName: 'Food Delivery MR',
      developerEmail: '<EMAIL>',
      developerPhone: '+222 45 67 89 14',
      companyName: 'Food Delivery SARL',
      developerWebsite: 'https://fooddelivery.mr',
      appType: AppType.mobile,
      supportedPlatforms: [Platform.android, Platform.iOS],
      iconUrl: 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=150&h=150&fit=crop&crop=center',
      screenshots: [
        'https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=600&fit=crop',
        'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=300&h=600&fit=crop',
      ],
      licenseType: LicenseType.free,
      pricingModel: PricingModel.enterprise,
      pricing: 'Frais de livraison',
      hasFreeTrial: false,
      trialDays: 0,
      targetAudience: 'Amateurs de cuisine locale',
      businessSectors: ['Restauration', 'Livraison', 'Gastronomie'],
      businessValue: 'Promeut la cuisine mauritanienne',
      keyFeatures: ['Cuisine authentique', 'Livraison rapide', 'Paiement mobile', 'Suivi commande'],
      supportOptions: [SupportType.phone, SupportType.chat],
      languages: ['Français', 'Arabe', 'Hassaniya'],
      downloads: 31000,
      activeUsers: 21000,
      publishDate: DateTime(2024, 4, 10),
      lastUpdate: DateTime(2024, 8, 15),
      isVerified: true,
      isFeatured: true,
      primaryCategory: 'food',
      subcategory: 'Livraison',
      tags: ['cuisine', 'livraison', 'thieboudienne', 'restaurant'],
      rating: 4.6,
    ),
  ];

  static List<MauritanianApp> get featuredApps =>
      apps.where((app) => app.isFeatured).toList();

  static List<MauritanianApp> getAppsByCategory(String category) =>
      apps.where((app) => app.primaryCategory == category).toList();

  static List<MauritanianApp> getAppsByAudience(String audience) =>
      apps.where((app) => app.targetAudience == audience).toList();

  static List<MauritanianApp> getAppsByDeveloperType(String type) =>
      apps.where((app) => app.companyName.contains(type)).toList();

  static List<MauritanianApp> searchApps(String query) {
    final lowercaseQuery = query.toLowerCase();
    return apps.where((app) {
      return app.appName.toLowerCase().contains(lowercaseQuery) ||
          app.description.toLowerCase().contains(lowercaseQuery) ||
          app.tags.any((tag) => tag.toLowerCase().contains(lowercaseQuery)) ||
          app.developerName.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  static MauritanianApp? getAppById(String id) {
    try {
      return apps.firstWhere((app) => app.id == id);
    } catch (e) {
      return null;
    }
  }

  static List<MauritanianApp> get recentApps {
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    return apps.where((app) => app.publishDate.isAfter(thirtyDaysAgo)).toList()
      ..sort((a, b) => b.publishDate.compareTo(a.publishDate));
  }

  static List<MauritanianApp> get topRatedApps {
    final sortedApps = List<MauritanianApp>.from(apps);
    sortedApps.sort((a, b) => b.rating.compareTo(a.rating));
    return sortedApps.take(10).toList();
  }

  static List<MauritanianApp> get mostDownloadedApps {
    final sortedApps = List<MauritanianApp>.from(apps);
    sortedApps.sort((a, b) => b.downloads.compareTo(a.downloads));
    return sortedApps.take(10).toList();
  }
}
